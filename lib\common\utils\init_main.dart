import 'dart:io';

import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/utils/local_notifications.dart';
import 'package:cal/common/utils/notifications.dart';
import 'package:cal/core/di/injection.config.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/authentication/di/authentication_injection.dart';
import 'package:cal/features/quick_actions/exercise/di/exercise_injection.dart';
import 'package:cal/features/subscriptions/di/subscription_injection.dart';
import 'package:cal/firebase_options.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../state_managment/bloc_observer.dart';

import 'shared_preferences_helper.dart';

class Initialization {
  static Future<void> initMain() async {
    WidgetsFlutterBinding.ensureInitialized();
    await getIt.init();

    if (!Platform.isWindows) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    AuthenticationInjection.init();
    initSubscriptionDependencies();
    initExercise();

    await ShPH.init();

    await updateCounter();

    Bloc.observer = AppBlocObserver();
  }

  static Future<void> updateCounter() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final lastLoginRaw = ShPH.getData(key: AppKeys.lastLoginDate);
    DateTime? lastLoginDate;

    if (lastLoginRaw is String) {
      lastLoginDate = DateTime.tryParse(lastLoginRaw);
    }

    int progressStreak = ShPH.getData(key: AppKeys.progressStreak) ?? 0;

    if (today.weekday == DateTime.saturday) {
      progressStreak = 0;
    } else {
      if (lastLoginDate == null || today.isAfter(DateTime(lastLoginDate.year, lastLoginDate.month, lastLoginDate.day))) {
        progressStreak++;
      }
    }

    await ShPH.saveData(key: AppKeys.progressStreak, value: progressStreak);
    await ShPH.saveData(key: AppKeys.lastLoginDate, value: today.toIso8601String());
  }

  static initNotifications() async {
    await LocalNotificationService.initialize();
    await NotificationService().initialize();
    await NotificationService().getToken();
  }

  static bool shouldShowWelcomeScreen() {
    final shouldShowWelcomeScreen = ShPH.getData(key: AppKeys.shouldShowWelcomeScreen) ?? true;

    return shouldShowWelcomeScreen;
  }

  static initLocalization(Widget main) {
    // Get saved locale from SharedPreferences, default to Arabic if not found
    final String? savedLocaleCode = ShPH.getData(key: AppKeys.appLanguage);
    final Locale savedLocale = savedLocaleCode != null ? Locale(savedLocaleCode) : const Locale('ar');

    return EasyLocalization(
      supportedLocales: const [Locale('ar'), Locale('en')],
      path: 'assets/translations',
      fallbackLocale: const Locale('ar'),
      startLocale: savedLocale,
      saveLocale: true,
      child: main,
    );
  }
}
