import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'exercise_model.g.dart';

ExerciseModel exerciseModelFromJson(str) => ExerciseModel.fromJson(str);

@Collection(inheritance: false)
// ignore: must_be_immutable
class ExerciseModel extends Equatable {
  Id id = Isar.autoIncrement;

  late String? typeEnglish;
  late String? typeArabic;

  /// in minutes

  late int? duration;

  /// ['high', 'mid', 'low']
  late String? intensity;

  late int? calories;

  late DateTime? date;

  @ignore
  bool isLoading = false;
  @ignore
  bool hasError;

  ExerciseModel({
    DateTime? date,
    this.typeEnglish,
    this.typeArabic,
    this.duration,
    this.intensity,
    this.calories,
    this.isLoading = false,
    this.hasError = false,
  }) : date = date ?? DateTime.now();

  ExerciseModel copyWith({
    Id? id,
    String? typeEnglish,
    String? typeArabic,
    int? duration,
    String? intensity,
    int? calories,
    DateTime? date,
    bool? isLoading,
    bool? hasError,
  }) {
    final copy = ExerciseModel(
      typeArabic: typeArabic ?? this.typeArabic,
      calories: calories ?? this.calories,
      typeEnglish: typeEnglish ?? this.typeEnglish,
      duration: duration ?? this.duration,
      intensity: intensity ?? this.intensity,
      date: date ?? this.date,
      hasError: hasError ?? this.hasError,
      isLoading: isLoading ?? this.isLoading,
    );

    copy.id = id ?? this.id;
    return copy;
  }

  factory ExerciseModel.fromJson(Map<String, dynamic> fullJson) {
    final json = fullJson['data'];
    return ExerciseModel(
      typeArabic: json['ar_type'] as String?,
      typeEnglish: json['en_type'] as String?,
      calories: json['calories'] as int,
      intensity: json['intensity'],
      duration: (json['duration'] ?? 0).toDouble(),
    );
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'dish': dish,
  //     'calories': calories,
  //     'protein': protein,
  //     'carbs': carbs,
  //     'fat': fat,
  //     'isHalal': isHalal,
  //     'date': date?.toIso8601String(),
  //     'imagePath': imagePath,
  //     'isLoading': isLoading,
  //     'english_name': englishName,
  //     'arabic_name': arabicName,
  //     'ingredients': ingredients
  //   };
  // }

  @override
  @ignore
  List<Object?> get props => [];
}
