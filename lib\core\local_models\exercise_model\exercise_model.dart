import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'exercise_model.g.dart';

ExerciseModel exerciseModelFromJson(str) => ExerciseModel.fromJson(str);

@Collection(inheritance: false)
// ignore: must_be_immutable
class ExerciseModel extends Equatable {
  Id id = Isar.autoIncrement;

  late String? typeEnglish;
  late String? typeArabic;

  /// in minutes
  late int? duration;

  /// ['high', 'mid', 'low']
  late String? intensity;

  late int? calories;

  late DateTime? date;

  /// For AI-based exercises
  late String? description;

  /// Exercise source: 'manual', 'ai'
  late String? source;

  /// MET value used for calculation
  late double? metValue;

  /// User weight at time of exercise (in kg)
  late double? userWeight;

  @ignore
  bool isLoading = false;
  @ignore
  bool hasError;

  ExerciseModel({
    DateTime? date,
    this.typeEnglish,
    this.typeArabic,
    this.duration,
    this.intensity,
    this.calories,
    this.description,
    this.source,
    this.metValue,
    this.userWeight,
    this.isLoading = false,
    this.hasError = false,
  }) : date = date ?? DateTime.now();

  ExerciseModel copyWith({
    Id? id,
    String? typeEnglish,
    String? typeArabic,
    int? duration,
    String? intensity,
    int? calories,
    DateTime? date,
    String? description,
    String? source,
    double? metValue,
    double? userWeight,
    bool? isLoading,
    bool? hasError,
  }) {
    final copy = ExerciseModel(
      typeArabic: typeArabic ?? this.typeArabic,
      calories: calories ?? this.calories,
      typeEnglish: typeEnglish ?? this.typeEnglish,
      duration: duration ?? this.duration,
      intensity: intensity ?? this.intensity,
      date: date ?? this.date,
      description: description ?? this.description,
      source: source ?? this.source,
      metValue: metValue ?? this.metValue,
      userWeight: userWeight ?? this.userWeight,
      hasError: hasError ?? this.hasError,
      isLoading: isLoading ?? this.isLoading,
    );

    copy.id = id ?? this.id;
    return copy;
  }

  factory ExerciseModel.fromJson(Map<String, dynamic> fullJson) {
    final json = fullJson['data'] ?? fullJson;
    return ExerciseModel(
      typeArabic: json['ar_type'] as String?,
      typeEnglish: json['en_type'] as String?,
      calories: json['calories'] as int?,
      intensity: json['intensity'] as String?,
      duration: json['duration'] as int?,
      description: json['description'] as String?,
      source: json['source'] as String?,
      metValue: (json['met_value'] as num?)?.toDouble(),
      userWeight: (json['user_weight'] as num?)?.toDouble(),
      date: json['date'] != null ? DateTime.parse(json['date']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'en_type': typeEnglish,
      'ar_type': typeArabic,
      'duration': duration,
      'intensity': intensity,
      'calories': calories,
      'description': description,
      'source': source,
      'met_value': metValue,
      'user_weight': userWeight,
      'date': date?.toIso8601String(),
    };
  }

  @override
  @ignore
  List<Object?> get props => [
    id,
    typeEnglish,
    typeArabic,
    duration,
    intensity,
    calories,
    description,
    source,
    metValue,
    userWeight,
    date,
  ];
}
