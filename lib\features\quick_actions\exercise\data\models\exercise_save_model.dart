import 'package:equatable/equatable.dart';
import 'package:dio/dio.dart';

class ExerciseSaveModel extends Equatable {
  final int calories;
  final String intensity;
  final int duration;

  const ExerciseSaveModel({
    required this.calories,
    required this.intensity,
    required this.duration,
  });

  /// Convert to form data for API submission
  FormData toFormData() {
    return FormData.fromMap({
      "calories": calories,
      "intensity": intensity,
      "duration": duration,
    });
  }

  /// Convert to JSON for local storage or debugging
  Map<String, dynamic> toJson() {
    return {
      "calories": calories,
      "intensity": intensity,
      "duration": duration,
    };
  }

  @override
  List<Object?> get props => [calories, intensity, duration];
}


