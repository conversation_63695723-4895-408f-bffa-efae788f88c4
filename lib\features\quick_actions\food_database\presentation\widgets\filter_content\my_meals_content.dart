import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/pages/create_meal_screen.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/enter_your_food_container.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../core/di/injection.dart';
import '../../../../../../core/local_models/food_model/food_model.dart';
import '../../../../../home/<USER>/bloc/recent_food_bloc.dart';
import '../../../data/models/database_food_model.dart';

class MyMealsContent extends StatefulWidget {
  final String searchQuery;

  const MyMealsContent({super.key, required this.searchQuery});

  @override
  State<MyMealsContent> createState() => _MyMealsContentState();
}

class _MyMealsContentState extends State<MyMealsContent> {
  @override
  void initState() {
    context.read<FoodDatabaseBloc>().add(const LoadMyMealsEvent());

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
      builder: (context, state) {
        final filteredMyMeals = state.myMealsList.where((meal) => meal.dish?.toLowerCase().contains(widget.searchQuery) ?? false).toList();

        return Column(
          children: [
            EnterYourFoodContainer(
              onPressed: () {
                context.push(const CreateMealScreen());
              },
              text: LocaleKeys.food_database_enter_your_meals.tr(),
              icon: const AppImage.asset(Assets.imagesEdit),
            ),
            const SizedBox(height: 10),
            state.myMealsList.isNotEmpty
                ? ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, i) => FoodDatabaseCard(
                        title: state.myMealsList[i].dish ?? "unknown",
                        cals: state.myMealsList[i].calories.toString(),
                        onAddTap: () {
                          getIt<RecentFoodBloc>().add(
                            AddFood(
                              isFromSearch: true,
                              meal: FoodModel(
                                remoteLogId: state.myMealsList[i].remoteLogId,
                                calories: state.myMealsList[i].calories,
                                fat: state.myMealsList[i].fat,
                                carbs: state.myMealsList[i].carbs,
                                protein: state.myMealsList[i].protein,
                                dish: state.myMealsList[i].dish,
                                ingredients: state.myMealsList[i].ingredients ?? [],
                                isHalal: null,
                                date: DateTime.now(),
                              ),
                            ),
                          );
                          context.read<FoodDatabaseBloc>().add(
                                AddFoodToLogEvent(
                                  isMeal: true,
                                  meal: FoodModel.fromDatabaseModel(
                                    DatabaseFoodModel(
                                      calories: state.myMealsList[i].calories,
                                      fat: state.myMealsList[i].fat,
                                      carbs: state.myMealsList[i].carbs,
                                      protein: state.myMealsList[i].protein,
                                      dish: state.myMealsList[i].dish,
                                      ingredients: state.myMealsList[i].ingredients,
                                      isFromSearch: true,
                                      isHalal: null,
                                      date: DateTime.now(),
                                    ),
                                  ),
                                ),
                              );
                          context.read<FoodDatabaseBloc>().add(
                                AddFoodEvent(
                                  meal: DatabaseFoodModel(
                                    calories: state.myMealsList[i].calories,
                                    fat: state.myMealsList[i].fat,
                                    carbs: state.myMealsList[i].carbs,
                                    protein: state.myMealsList[i].protein,
                                    dish: state.myMealsList[i].dish,
                                    ingredients: state.myMealsList[i].ingredients ?? [],
                                    isHalal: null,
                                    date: DateTime.now(),
                                  ),
                                ),
                              );
                          Future.delayed(const Duration(milliseconds: 200), () {
                            if (context.mounted) getIt<RecentFoodBloc>().add(LoadFood(date: DateTime.now()));
                            if (context.mounted) context.pop();
                          });
                        }),
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                    itemCount: filteredMyMeals.length,
                  )
                : Align(
                    alignment: Alignment.center,
                    child: AppText.bodyMedium(
                      LocaleKeys.food_database_no_food.tr(),
                      color: context.onSecondary,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
          ],
        );
      },
    );
  }
}
