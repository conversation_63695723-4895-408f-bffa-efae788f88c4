import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/home/<USER>/repositories/exercise_repository.dart';
import 'package:cal/features/quick_actions/exercise/data/datasources/exercise_local_datasource.dart';
import 'package:injectable/injectable.dart';

@injectable
class HomeExerciseRepositoryImpl implements HomeExerciseRepository {
  final ExerciseLocalDataSource exerciseLocalDataSource;

  HomeExerciseRepositoryImpl({required this.exerciseLocalDataSource});

  @override
  Future<List<ExerciseModel>> getAllExercises(DateTime date) async {
    return await exerciseLocalDataSource.getAllExercises(date);
  }

  @override
  Future<void> deleteExercise(ExerciseModel exercise) async {
    await exerciseLocalDataSource.deleteExercise(exercise);
  }

  @override
  Future<void> clearExercises() async {
    await exerciseLocalDataSource.clearExercises();
  }
}
