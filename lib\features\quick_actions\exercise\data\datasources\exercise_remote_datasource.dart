import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:dartz/dartz.dart';

abstract class ExerciseRemoteDataSource {
  Future<Either<Failure, ExerciseModel>> saveExercise(ExerciseSaveModel exercise);
  Future<Either<Failure, ExerciseModel>> saveExerciseAi(ExerciseSaveAiModel exercise);
}

class ExerciseRemoteDataSourceImpl implements ExerciseRemoteDataSource {
  final HTTPClient httpClient;

  ExerciseRemoteDataSourceImpl({required this.httpClient});

  @override
  Future<Either<Failure, ExerciseModel>> saveExercise(ExerciseSaveModel exercise) async {
    try {
      final response = await httpClient.post(
        '/api/exercise/save',
        data: exercise.toFormData(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return Right(ExerciseModel.fromJson(response.data));
      } else {
        return Left(ServerFailure(message: 'Failed to save exercise: ${response.statusCode}'));
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ExerciseModel>> saveExerciseAi(ExerciseSaveAiModel exercise) async {
    try {
      final response = await httpClient.post(
        '/api/exercise/saveAi',
        data: exercise.toFormData(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return Right(ExerciseModel.fromJson(response.data));
      } else {
        return Left(ServerFailure(message: 'Failed to save AI exercise: ${response.statusCode}'));
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
