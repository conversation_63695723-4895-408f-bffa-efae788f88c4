import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/datasources/streak_local_data_source.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:cal/features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/features/home/<USER>/widgets/food_card/food_card.dart';
import 'package:cal/features/home/<USER>/widgets/exercise_card.dart';
import 'package:cal/features/home/<USER>/widgets/home_appbar.dart';
import 'package:cal/features/home/<USER>/widgets/nutritions_summery.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:isar/isar.dart';

import '../../../../common/widgets/metric_card.dart';
import '../widgets/date_list.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ValueNotifier<DateTime> _dateValueNotifier = ValueNotifier<DateTime>(DateTime.now());

  @override
  void initState() {
    super.initState();

    _dateValueNotifier.addListener(_onDateChanged);
    // _onDateChanged();
  }

  @override
  void dispose() {
    _dateValueNotifier.removeListener(_onDateChanged);
    _dateValueNotifier.dispose();
    super.dispose();
  }

  void _onDateChanged() {
    context.read<RecentActivityBloc>().add(LoadActivity(date: _dateValueNotifier.value));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<NutritionBloc>(
      lazy: false,
      create: (context) => getIt<NutritionBloc>()..add(InitDailyUserData(date: _dateValueNotifier.value)),
      child: BlocListener<RecentActivityBloc, RecentActivityState>(
        listener: (context, state) {
          Future.delayed(const Duration(seconds: 1), () {
            if (context.mounted) {
              context.read<NutritionBloc>().add(LoadDailyNutritionData(date: _dateValueNotifier.value));
            }
          });
        },
        child: Scaffold(
          appBar: homeAppBar(context),
          body: SingleChildScrollView(
            padding: const EdgeInsetsDirectional.only(end: 16, start: 16, top: 0, bottom: 50),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<NutritionBloc, NutritionState>(
                  builder: (context, state) {
                    return DateList(
                      selectedDateNotifier: _dateValueNotifier,
                      todayCals: calculateSafeProgress(state.targetCalories, state.consumedCalories),
                    );
                  },
                ),
                const SizedBox(height: 20),
                const NutritionSummary(),
                const SizedBox(height: 30),
                AppText.titleLarge(LocaleKeys.home_recently_added.tr(), color: context.onSecondary, fontWeight: FontWeight.bold),
                const SizedBox(height: 20),
                BlocBuilder<RecentActivityBloc, RecentActivityState>(
                  builder: (context, state) {
                    if (state.foodList.isNotEmpty || state.exerciseList.isNotEmpty) {
                      final day = DateTime.now();
                      StreakLocalDataSource(getIt<Isar>())
                          .saveStreak(StreakModel(hasAction: true, streakDate: DateTime(day.year, day.month, day.day)));
                    }

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Food Cards Section
                        if (state.foodList.isNotEmpty) ...[
                          AnimationLimiter(
                            key: Key(state.foodList.map((food) => "${food.id}_${food.isLoading}_${food.hasError}").join("_")),
                            child: ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: state.foodList.length,
                              itemBuilder: (context, index) {
                                return AnimationConfiguration.staggeredList(
                                  position: index,
                                  duration: const Duration(milliseconds: 375),
                                  child: SlideAnimation(
                                    verticalOffset: 50.0,
                                    child: FadeInAnimation(
                                      child: FoodCard(
                                        foodModel: state.foodList[index],
                                        onDelete: () => context.read<RecentActivityBloc>().add(DeleteFood(state.foodList[index])),
                                        onRetry: () => context.read<ScanFoodBloc>().add(const RetryRecognizeFoodEvent()),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) => const SizedBox(height: 16),
                            ),
                          ),
                        ],

                        // Exercise Cards Section
                        if (state.exerciseList.isNotEmpty) ...[
                          if (state.foodList.isNotEmpty) const SizedBox(height: 30),
                          AppText.titleLarge(
                            'التمارين اليوم',
                            color: context.onSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          const SizedBox(height: 20),
                          AnimationLimiter(
                            key: Key(state.exerciseList.map((exercise) => "${exercise.id}_${exercise.date}").join("_")),
                            child: ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: state.exerciseList.length,
                              itemBuilder: (context, index) {
                                return AnimationConfiguration.staggeredList(
                                  position: index + state.foodList.length,
                                  duration: const Duration(milliseconds: 375),
                                  child: SlideAnimation(
                                    verticalOffset: 50.0,
                                    child: FadeInAnimation(
                                      child: ExerciseCard(
                                        exerciseModel: state.exerciseList[index],
                                        onDelete: () => context.read<RecentActivityBloc>().add(DeleteExercise(state.exerciseList[index])),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) => const SizedBox(height: 16),
                            ),
                          ),
                        ],

                        // Empty state
                        if (state.foodList.isEmpty && state.exerciseList.isEmpty)
                          const SizedBox.shrink(),
                      ],
                    );
                  },
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
