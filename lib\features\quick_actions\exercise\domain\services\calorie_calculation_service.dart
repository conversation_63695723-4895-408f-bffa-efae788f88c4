import 'package:cal/core/datasources/user_local_data_source.dart';
import 'package:injectable/injectable.dart';

/// Service for calculating calories burned during exercise using MET values
@injectable
class CalorieCalculationService {
  final UserLocalDataSource _userLocalDataSource;

  CalorieCalculationService(this._userLocalDataSource);

  /// MET values for different exercise types and intensities
  static const Map<String, Map<String, double>> _metValues = {
    'run': {
      'low': 6.0,    // Light jogging (5 mph)
      'mid': 8.3,    // Running (6 mph)
      'high': 11.0,  // Running (7 mph)
    },
    'weight_lifting': {
      'low': 3.0,    // Light effort
      'mid': 5.0,    // Moderate effort
      'high': 6.0,   // Vigorous effort
    },
    'cycling': {
      'low': 4.0,    // Leisure cycling
      'mid': 6.8,    // Moderate cycling
      'high': 8.5,   // Vigorous cycling
    },
    'walking': {
      'low': 2.5,    // Slow pace
      'mid': 3.5,    // Moderate pace
      'high': 4.3,   // Brisk pace
    },
    'swimming': {
      'low': 4.8,    // Leisure swimming
      'mid': 7.0,    // Moderate swimming
      'high': 10.0,  // Vigorous swimming
    },
    'yoga': {
      'low': 2.5,    // Gentle yoga
      'mid': 3.0,    // Hatha yoga
      'high': 4.0,   // Power yoga
    },
    'general': {
      'low': 3.0,    // Default low intensity
      'mid': 5.0,    // Default moderate intensity
      'high': 7.0,   // Default high intensity
    },
  };

  /// Calculate calories burned using the formula: Calories = MET × Weight × Time (in hours)
  /// 
  /// [exerciseType] - Type of exercise (run, weight_lifting, etc.)
  /// [intensity] - Intensity level (low, mid, high)
  /// [durationMinutes] - Duration of exercise in minutes
  /// [userWeight] - Optional user weight in kg. If null, will fetch from user data
  /// 
  /// Returns a [CalorieCalculationResult] containing calories burned and MET value used
  Future<CalorieCalculationResult> calculateCalories({
    required String exerciseType,
    required String intensity,
    required int durationMinutes,
    double? userWeight,
  }) async {
    // Get user weight if not provided
    final weight = userWeight ?? await _getUserWeight();
    
    if (weight == null || weight <= 0) {
      throw Exception('User weight not available or invalid');
    }

    // Get MET value for the exercise type and intensity
    final metValue = _getMetValue(exerciseType, intensity);
    
    // Convert duration to hours
    final durationHours = durationMinutes / 60.0;
    
    // Calculate calories: MET × Weight (kg) × Time (hours)
    final calories = (metValue * weight * durationHours).round();
    
    return CalorieCalculationResult(
      calories: calories,
      metValue: metValue,
      userWeight: weight,
      durationHours: durationHours,
    );
  }

  /// Get MET value for specific exercise type and intensity
  double _getMetValue(String exerciseType, String intensity) {
    final normalizedType = exerciseType.toLowerCase().replaceAll(' ', '_');
    final normalizedIntensity = intensity.toLowerCase();
    
    // Try to find exact match
    if (_metValues.containsKey(normalizedType)) {
      return _metValues[normalizedType]![normalizedIntensity] ?? 
             _metValues[normalizedType]!['mid'] ?? 
             _metValues['general']![normalizedIntensity]!;
    }
    
    // Fall back to general values
    return _metValues['general']![normalizedIntensity] ?? 5.0;
  }

  /// Get user weight from local storage
  Future<double?> _getUserWeight() async {
    try {
      final user = await _userLocalDataSource.getUserData();
      if (user?.weight != null) {
        return double.tryParse(user!.weight!);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get available exercise types
  static List<String> getAvailableExerciseTypes() {
    return _metValues.keys.where((key) => key != 'general').toList();
  }

  /// Get available intensity levels
  static List<String> getAvailableIntensityLevels() {
    return ['low', 'mid', 'high'];
  }

  /// Get MET value for display purposes (without calculation)
  static double getMetValueForDisplay(String exerciseType, String intensity) {
    final normalizedType = exerciseType.toLowerCase().replaceAll(' ', '_');
    final normalizedIntensity = intensity.toLowerCase();
    
    if (_metValues.containsKey(normalizedType)) {
      return _metValues[normalizedType]![normalizedIntensity] ?? 
             _metValues[normalizedType]!['mid'] ?? 
             _metValues['general']![normalizedIntensity]!;
    }
    
    return _metValues['general']![normalizedIntensity] ?? 5.0;
  }
}

/// Result of calorie calculation
class CalorieCalculationResult {
  final int calories;
  final double metValue;
  final double userWeight;
  final double durationHours;

  const CalorieCalculationResult({
    required this.calories,
    required this.metValue,
    required this.userWeight,
    required this.durationHours,
  });

  @override
  String toString() {
    return 'CalorieCalculationResult(calories: $calories, metValue: $metValue, userWeight: $userWeight, durationHours: $durationHours)';
  }
}
