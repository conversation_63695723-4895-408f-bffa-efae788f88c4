import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

abstract class ExerciseLocalDataSource {
  Future<void> saveExercise(ExerciseModel exercise);
  Future<List<ExerciseModel>> getAllExercises(DateTime date);
  Future<List<ExerciseModel>> getExercisesByDateRange(DateTime startDate, DateTime endDate);
  Future<void> updateExercise(ExerciseModel updatedExercise);
  Future<void> deleteExercise(ExerciseModel exercise);
  Future<void> clearExercises();
}

@injectable
class ExerciseLocalDataSourceImpl implements ExerciseLocalDataSource {
  final Isar _isar;

  ExerciseLocalDataSourceImpl(this._isar);

  @override
  Future<void> saveExercise(ExerciseModel exercise) async {
    await _isar.writeTxn(() async {
      await _isar.exerciseModels.put(exercise);
    });
  }

  @override
  Future<List<ExerciseModel>> getAllExercises(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
    
    return await _isar.exerciseModels
        .filter()
        .dateBetween(startOfDay, endOfDay)
        .sortByDateDesc()
        .findAll();
  }

  @override
  Future<List<ExerciseModel>> getExercisesByDateRange(DateTime startDate, DateTime endDate) async {
    final startOfDay = DateTime(startDate.year, startDate.month, startDate.day);
    final endOfDay = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
    
    return await _isar.exerciseModels
        .filter()
        .dateBetween(startOfDay, endOfDay)
        .sortByDateDesc()
        .findAll();
  }

  @override
  Future<void> updateExercise(ExerciseModel updatedExercise) async {
    await _isar.writeTxn(() async {
      await _isar.exerciseModels.put(updatedExercise);
    });
  }

  @override
  Future<void> deleteExercise(ExerciseModel exercise) async {
    await _isar.writeTxn(() async {
      await _isar.exerciseModels.delete(exercise.id);
    });
  }

  @override
  Future<void> clearExercises() async {
    await _isar.writeTxn(() async {
      await _isar.exerciseModels.clear();
    });
  }
}
